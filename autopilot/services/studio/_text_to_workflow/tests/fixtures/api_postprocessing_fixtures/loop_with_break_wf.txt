```yaml
input: '{"type": "object", "properties": {"ticketId": {"type": "string"}}}'
root:
  thought: Sequence
  activity: Sequence
  id: Sequence_1
  do:
  - thought: Search FreshService tickets opened in the last 2 weeks
    activity: FreshserviceSearch_Tickets
    id: query_tickets_1
    with:
      query: ${"created_at:>'"+ new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10) + "'"}
  - thought: Loop through the tickets
    activity: ForEach
    id: ForEach_1
    for:
      each: currentTicket
      in: $context.outputs.query_tickets_1.content
      at: currentIndex
    do:
    - thought: Check if the ticket status is closed
      activity: If
      id: If_1
      condition: ${$currentTicket.status == "Closed"}
      then:
      - thought: Break the loop
        activity: Break
        id: Break_1
      else:
      - thought: Send an <NAME_EMAIL>
        activity: Microsoft_Outlook_365Send_Email
        id: Microsoft_Outlook_365Send_Email_1
        with:
          message.toRecipients: <EMAIL>
          message.subject: ${"FreshService Ticket: " + $currentTicket.id}
          message.body.content: ${"Ticket details: " + $currentTicket.description}
```