from pydantic import BaseModel, <PERSON>

from services.studio._text_to_workflow.models.output_parsers import MinifiedPydanticOutputParser


class WorkflowResult(BaseModel):
    strengths: list[str] = Field(description="list of positive aspects")
    syntax_issues: list[str] = Field(description="YAML or coding syntax problems")
    relevance_issues: list[str] = Field(description="query alignment problems")
    correctness_issues: list[str] = Field(description="logical problems")
    hallucination_issues: list[str] = Field(description="irrelevant/incorrect elements")
    structure_issues: list[str] = Field(description="bad activity order/tree structure")
    parameter_issues: list[str] = Field(description="parameter value problems")
    score: int = Field(description="a score between 1 and 5, representing the quality of the workflow")

class QueryResult(BaseModel):
    strengths: list[str] = Field(description="list of positive aspects")
    task_appropriateness_issues: list[str] = Field(description="inappropriate for automation. e.g. this can't be solved with code execution, spam or nonsensical queries")
    ambiguity_issues: list[str] = Field(description="multiple interpretations possible or unclear")
    completeness_issues: list[str] = Field(description="missing details (only few parameters can be omitted and given at runtime, but not applications)")
    score: int = Field(description="a score between 1 and 5, representing the quality of the query")

class JudgeResult(BaseModel):
    prediction: WorkflowResult = Field(description="evaluation of the workflow code predicted by a LLM to automate the query")
    ground_truth: WorkflowResult = Field(description="evaluation of the workflow code saved in our dataset as a reference on how to automate the query")
    query: QueryResult = Field(description="evaluation of the query to be automated")

judge_parser = MinifiedPydanticOutputParser(pydantic_object=JudgeResult)

class ProblemPattern(BaseModel):
    description: str = Field(description="description of the pattern")
    examples: list[str] = Field(description="list of examples of this pattern from the set to summarize")
    count: int = Field(description="number of times the pattern was found")

class JudgeSummaryResult(BaseModel):
    problem_patterns: list[ProblemPattern] = Field(description="list of problem patterns found with examples and counts")

judge_summary_parser = MinifiedPydanticOutputParser(pydantic_object=JudgeSummaryResult)